{"version": "0.2", "language": "en", "caseSensitive": false, "$schema": "https://raw.githubusercontent.com/streetsidesoftware/cspell/main/cspell.schema.json", "words": ["Mvdol", "Botswanan", "Verdean", "Rican", "Djiboutian", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Guyanaese", "Kazakhstani", "Kyrgystani", "Talon<PERSON>", "<PERSON><PERSON>", "Cupon", "rdoba", "RINET", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "ncipe", "Tajikistani", "So<PERSON><PERSON>", "Timorese", "anga", "Turkmenistani", "Ka<PERSON>ova<PERSON>", "Uzbekistani", "Zairean", "<PERSON><PERSON><PERSON>", "Ababa", "accepte", "Accordian", "Adak", "<PERSON><PERSON>", "adipiscing", "airbyte", "Akim", "alertable", "aliqua", "alldays", "allteams", "alluser", "amet", "<PERSON><PERSON><PERSON>", "<PERSON>ross", "<PERSON><PERSON><PERSON>", "animatable", "apicivo", "a<PERSON>w", "apida", "apidemo", "apidemocivo", "apidemocw", "apidemoda", "apidem<PERSON><PERSON>", "apidemod<PERSON>", "<PERSON><PERSON><PERSON>", "apikey", "apisauce", "apistage", "apistagecivo", "apistagecw", "apistageda", "apistagedt", "apistagedts", "apos", "appbaseio", "appdev", "appkey", "applesimutils", "APPSTORE", "<PERSON><PERSON><PERSON><PERSON>", "Aqtobe", "Araguaina", "<PERSON><PERSON>", "arrowleft", "asar", "asel", "Asmer<PERSON>", "Atyrau", "Authentificate", "authjs", "autorun", "backoff", "Banderas", "<PERSON><PERSON><PERSON>", "barcodes", "<PERSON><PERSON><PERSON><PERSON>", "baseurls", "billrate", "binutils", "Blandit", "blueprintjs", "Boilerplates", "bootsplash", "borde", "<PERSON>ser", "Branco", "Btns", "Bugsnag", "buildjet", "<PERSON><PERSON><PERSON><PERSON>", "Busingen", "cacheable", "camelcase", "capitaliz", "Catamarca", "<PERSON><PERSON>", "<PERSON><PERSON>", "Changemail", "Charaters", "Chatwoot", "cheched", "checkcircle", "checkcircleo", "chetwode", "<PERSON><PERSON><PERSON>", "choos", "ciphertext", "classpath", "Clik", "cloc", "clockcircleo", "cloudinary", "clsx", "clsxm", "Cmbx", "cmdk", "Codacy", "codecov", "Codementor", "collapsable", "Combox", "combx", "commitlint", "comparision", "comparization", "compat", "compodoc", "configurator", "consectetur", "containerd", "<PERSON><PERSON><PERSON>", "conver", "creatoe", "Creston", "cssnano", "CUBEJS", "<PERSON><PERSON><PERSON>", "customStyle", "dailyplan", "Danmarkshavn", "Darkmode", "DATACENTER", "datas", "dataToDisplay", "datetime", "daygrid", "DAYOFWEEK", "dearmor", "deepscan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Desabled", "Descrption", "deserunt", "digitaloceanspaces", "<PERSON><PERSON>", "dimesions", "discoverability", "discrepenancy", "dolor", "dolore", "<PERSON><PERSON><PERSON><PERSON>", "dompurify", "domutils", "DONT", "dotenv", "dpkg", "Dropown", "dropwdown", "dummyimage", "<PERSON><PERSON>", "dvalue", "Efate", "Eirunepe", "<PERSON><PERSON><PERSON><PERSON>", "electronmon", "<PERSON><PERSON><PERSON>", "elit", "ellipsize", "embla", "<PERSON><PERSON><PERSON>", "endregion", "Entra", "<PERSON><PERSON><PERSON>", "envalid", "Environtment", "errr", "<PERSON><PERSON><PERSON>", "eventnative", "everco", "evereq", "everteamsdesktop", "everteamswebserver", "exalidraw", "excalidraw", "exclamationcircleo", "expanded", "exposdk", "extramenu", "<PERSON><PERSON><PERSON><PERSON>", "falsey", "<PERSON><PERSON><PERSON>", "fbjs", "Ffeeds", "<PERSON>lder", "filtmembers", "firstname", "flaticon", "flexbox", "fomated", "Formated", "FORYOU", "fullcalendar", "FULLNAME", "Funtion", "Galery", "gantt", "gauzystage", "gcloud", "gitops", "Gitter", "GlobalSkeleton", "Gloire", "gorhom", "gradlew", "graphicsmagick", "greenkeeper", "greppable", "grotesk", "gtag", "<PERSON><PERSON>", "h<PERSON><PERSON>a", "HCAPTCHA", "headlessui", "healthcheck", "<PERSON><PERSON>", "heroicons", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hidde", "hitbox", "<PERSON><PERSON><PERSON>", "hookform", "ho<PERSON><PERSON><PERSON>", "Hovd", "HUBSTAFF", "<PERSON><PERSON>", "huntr", "hwba", "hyperscript", "<PERSON><PERSON><PERSON>", "icns", "icnsutils", "Icontent", "imagebutton", "im<PERSON>", "immediatly", "imodal", "implem", "INAPP", "incididunt", "INFOTMATION", "inital", "initdb", "instanceof", "Instanciate", "insted", "intefaces", "Intervall", "Inuvik", "invdate", "Invitatio", "invitationid", "Invitions", "Ionicons", "iphonesimulator", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ipsum", "isdragging", "isdraggingfrom", "isdropdisabled", "Isssue", "ISSUETYPE", "<PERSON><PERSON><PERSON>", "itask", "iuser", "<PERSON><PERSON>", "jitsi", "JITSU", "jitsucom", "jsbundle", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "kanban", "Kanbanboard", "kanband<PERSON>", "keyrings", "Khandyga", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "knip", "Kolkata", "Konviser", "Kosrae", "<PERSON><PERSON><PERSON>", "Kralendijk", "krisp", "<PERSON><PERSON>", "Kuala", "labore", "languagedetector", "Las<PERSON>", "lastest", "lastlog", "lgcard", "libappindicator", "<PERSON><PERSON><PERSON>", "livekit", "livekitroom", "Lngs", "Loadtasks", "lobortis", "localstorage", "locatio", "lockfiles", "locutus", "loglevel", "longpress", "Longyearbyen", "<PERSON><PERSON>", "lucide", "Lumpur", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "mailchimp", "mainconfig", "Managment", "Manaus", "mappagination", "Marigot", "Marino", "<PERSON><PERSON><PERSON><PERSON>", "maximizable", "<PERSON><PERSON>", "memlock", "metainfo", "Metlakatla", "Metral", "MICROSOFTENTRAID", "mi<PERSON><PERSON><PERSON><PERSON>", "millisencods", "MINIO", "mmkv", "mobx", "Moresby", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ncipollo", "Ndekocode", "neque", "<PERSON><PERSON>", "Neue", "nextjs", "nimg", "Nipigon", "nivo", "nocheck", "nodesource", "nodistro", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Northflank", "Notif", "nsis", "nums", "offcanvas", "<PERSON><PERSON><PERSON>", "oklch", "onwarn", "Opena", "opentelemetry", "openzipkin", "Ordereds", "Organizatio", "OSTYPE", "otlp", "Ouagadougou", "outclick", "overlapper", "pageleave", "pageview", "pageviews", "Pago", "pako", "pangea", "Pangnirtung", "<PERSON><PERSON><PERSON>", "paren", "<PERSON><PERSON><PERSON>", "passcode", "<PERSON><PERSON><PERSON>", "payperiod", "Periode", "<PERSON><PERSON><PERSON>", "persistance", "Personnal", "pgweb", "phoneinput", "phraseapp", "Pictogrammers", "pkill", "Plaholder", "plan", "plasmo", "plasmohq", "<PERSON><PERSON><PERSON>", "pmmmwh", "popper<PERSON>s", "posthog", "POSTHOG", "Pourt<PERSON>", "<PERSON><PERSON><PERSON>", "prebuild", "precommit", "Prefs", "prejoin", "Pressable", "Pressible", "progess", "Proguard", "Propf", "psql", "publicactive", "Puerto Rico", "<PERSON><PERSON>", "Q<PERSON><PERSON><PERSON>", "Qyzylorda", "reactotron", "Reactotron's", "RECAPTCHA", "recieve", "Reconds", "refetched", "Relationnal", "Repobeats", "RESERVERD", "retrocompatibility", "Retryable", "Rica", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "rounded", "runned", "<PERSON><PERSON><PERSON>", "Sablon", "Santarem", "Santo", "Scoresbysund", "Screenshoot", "screenshoots", "SCREENSHOOTS", "screenstatus", "sentryclirc", "<PERSON><PERSON><PERSON><PERSON>", "serverweb", "set<PERSON>e", "Settingfilter", "settingsCloseButton", "setuptools", "setwin", "setwork", "shadcn", "shandow", "signin", "<PERSON><PERSON>", "signoff", "simplecast", "sitekey", "skey", "smalltext", "smpt", "snyk", "sonner", "<PERSON><PERSON>", "so<PERSON>off", "Srednekolymsk", "sslmode", "stackoverflow", "statsus", "statut", "STMP", "strock", "stylelint", "stylesheet", "subsquently", "svgr", "svgs", "Swith", "<PERSON><PERSON>", "Synk", "Syowa", "Tabler", "tailess", "<PERSON><PERSON>", "tailwindcss", "tanstack", "taskid", "taskstatus", "tast", "tblr", "teamsupercell", "teamtask", "TEAMTASKS", "tempor", "testid", "timegrid", "Timesheet", "timesheet-viewMode", "Timesheets", "Timeslot", "tinvitations", "titleBar", "tnode", "<PERSON><PERSON>", "tomorrow", "Tongatapu", "tota", "TRANSFERT", "Transpiles", "<PERSON><PERSON><PERSON>", "tsbuildinfo", "t<PERSON><PERSON>", "tsup", "twing", "typeof", "uicolors", "uidotdev", "UIUX", "Ulaanbaatar", "ulimits", "unasigned", "unchild", "unhang", "unimodules", "unoffic", "unoptimized", "Unplan", "UNSTRINGIFIABLE", "UPWORK", "Urville", "usehooks", "Ushuaia", "Uturn", "Uzhgorod", "Varibal<PERSON>", "vaul", "vcpu", "Vefified", "<PERSON><PERSON><PERSON>", "Vercel", "<PERSON><PERSON><PERSON><PERSON>", "VERSONS", "vertificalline", "<PERSON><PERSON><PERSON>", "vhidden", "<PERSON><PERSON><PERSON><PERSON>", "Waitlist", "WARNING️", "wasabi<PERSON>s", "webm", "webp", "wght", "Winamac", "withteam", "worksace", "Worspace", "X", "xcodebuild", "xcodeproj", "xcworkspace", "Xlarge", "xlcard", "xlight", "xpack", "<PERSON><PERSON><PERSON>", "yellowbox", "zipkin", "Złoty", "zxcvbn"], "useGitignore": true, "ignorePaths": [".deploy/*", ".git/*", ".git/!{COMMIT_EDITMSG,EDITMSG}", ".git/*/**", ".yarn", "**/*.jar", ".pnp.js", "**/.git/**", ".vscode", ".giti<PERSON>re", "action/lib/**", "coverage", ".cspell.json", "cspell.json", "__snapshots__", "__recordings__", "**/coverage/**", "**/fixtures/**/*.json", "**/fixtures/sampleCode/*errors/", "**/node_modules/**", "**/vscode-extension/**", "package-lock.json", "yarn.lock", "**/assets/i18n/*.json", "**/migrations/**", "packages/**/*.seed.json", "**/*.svg", "tools/build/webpack.config.js", "docker-compose.demo.yml", "docker-compose.yml", "wait", "signin", "Chatwoot", "CHATWOOT", "apps/web/locales/*.json", "apps/web/i18n.ts", "apps/web/lib/i18n/*.ts", "apps/web/icons/icons.tsx", "apps/web/lib/settings/timezones.js", "apps/mobile/app/i18n/*.ts", "apps/mobile/android/**", "apps/mobile/ios/**", "apps/desktop/i18n/**", "apps/**/*.{svg,css,scss}", ".scripts/icon-utils/icons/**", "apps/server-web/src/locales/**/*.json", "apps/server-web/src/resources/*", "apps/server-web/assets/*", "apps/mobile/package.json", "apps/mobile/app/screens/demo-show-room-screen/**", "**/demo-*.tsx"]}
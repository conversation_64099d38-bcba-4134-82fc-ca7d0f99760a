@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
	--tw-color-dark--theme: #1e2025;
	--tooltipBackground: #000;
}
html {
	background: lch(96.667% 0 282.863 / 1);
}
html.dark {
	--tooltipBackground: #000;
}
.go2812612974 {
	z-index: 99999;
}
.font-poppins {
	font-family: 'Poppins', sans-serif !important;
}
*,
*::before,
*::after {
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;
	font-variant-ligatures: none;
	-webkit-font-variant-ligatures: none;
	text-rendering: optimizeLegibility;
}
@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 222.2 84% 4.9%;

		--muted: 210 40% 96.1%;
		--muted-foreground: 215.4 16.3% 46.9%;

		--popover: 0 0% 100%;
		--popover-foreground: 222.2 84% 4.9%;

		--card: 0 0% 100%;
		--card-foreground: 222.2 84% 4.9%;

		--border: 214.3 31.8% 91.4%;
		--input: 214.3 31.8% 91.4%;

		--primary: 222.2 47.4% 11.2%;
		--primary-foreground: 210 40% 98%;

		--secondary: 210 40% 96.1%;
		--secondary-foreground: 222.2 47.4% 11.2%;

		--accent: 210 40% 96.1%;
		--accent-foreground: 222.2 47.4% 11.2%;

		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 210 40% 98%;

		--ring: 215 20.2% 65.1%;

		--radius: 0.5rem;

		--sidebar-background: 0 0% 98%;

		--sidebar-foreground: 240 5.3% 26.1%;

		--sidebar-primary: 240 5.9% 10%;

		--sidebar-primary-foreground: 0 0% 98%;

		--sidebar-accent: 240 4.8% 95.9%;

		--sidebar-accent-foreground: 240 5.9% 10%;

		--sidebar-border: 220 13% 91%;

		--sidebar-ring: 217.2 91.2% 59.8%;
	}

	html,
	body {
		overflow-x: hidden;
	}
	.dark {
		--background: 222.2 84% 4.9%;
		--foreground: 210 40% 98%;

		--muted: 217.2 32.6% 17.5%;
		--muted-foreground: 215 20.2% 65.1%;

		--popover: 222.2 84% 4.9%;
		--popover-foreground: 210 40% 98%;

		--card: 222.2 84% 4.9%;
		--card-foreground: 210 40% 98%;

		--border: 217.2 32.6% 17.5%;
		--input: 217.2 32.6% 17.5%;

		--primary: 210 40% 98%;
		--primary-foreground: 222.2 47.4% 11.2%;

		--secondary: 217.2 32.6% 17.5%;
		--secondary-foreground: 210 40% 98%;

		--accent: 217.2 32.6% 17.5%;
		--accent-foreground: 210 40% 98%;

		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 0 85.7% 97.3%;

		--ring: 217.2 32.6% 17.5%;

		--sidebar-background: 240 5.9% 10%;

		--sidebar-foreground: 240 4.8% 95.9%;

		--sidebar-primary: 224.3 76.3% 48%;

		--sidebar-primary-foreground: 0 0% 100%;

		--sidebar-accent: 240 3.7% 15.9%;

		--sidebar-accent-foreground: 240 4.8% 95.9%;

		--sidebar-border: 240 3.7% 15.9%;

		--sidebar-ring: 217.2 91.2% 59.8%;
	}
	* {
		@apply border-border;
	}

	body {
		font-family:
			var(--font-sans), 'Plus-Jakarta-Sans-VariableFont_wght', 'Inter', 'Geist Sans', ui-sans-serif, system-ui,
			sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji' !important;
		font-style: normal;
		font-weight: 400;
		line-height: 1.5;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		text-rendering: optimizeLegibility;
		-webkit-tap-highlight-color: transparent;
		-webkit-touch-callout: none;
		@apply text-default dark:text-white bg-[rgb(242,242,242)] dark:bg-background text-foreground text-sm;
	}

	.x-container {
		@apply 3xl:max-w-[1600px] 2xl:max-w-[1400px] xl:max-w-[1280px];
	}
}

@layer components {
	/* Colors custom */
	.bg-gray-lighter {
		@apply bg-[#F2F2F2] dark:bg-dark--theme-light;
	}

	/* --------------------- Container ------------------------------------ */
	.x-container {
		--tblr-gutter-x: 1.5rem;
		--tblr-gutter-y: 0;
		width: 100%;
		padding-right: calc(var(--tblr-gutter-x) * 0.5);
		padding-left: calc(var(--tblr-gutter-x) * 0.5);
		margin-right: auto;
		margin-left: auto;
	}

	/* ----------------Fade IN and Fade out */

	.fade-out {
		opacity: 0;
		width: 0;
		height: 0;
		transition:
			width 0.5s 0.5s,
			height 0.5s 0.5s,
			opacity 0.5s;
	}
	.fade-in {
		opacity: 1;
		width: 100%;
		height: 100%;
		transition:
			width 0.2s,
			height 0.2s,
			opacity 0.2s 0.2s;
	}

	/* ------------------- Input ----------------------- */
	.input-border {
		@apply border-[#00000021] dark:border-[#3b3c44] border-solid border;
		@apply dark:bg-[#1B1D22];
	}

	/* Shadow */
	.nav-items--shadow {
		box-shadow: 0px 14px 24px -10px rgba(0, 0, 0, 0.04) !important;
	}

	.main--card-border {
		@apply border dark:border-[#28292F];
	}

	/* Tooplip  */

	.tooltip-container,
	.tooltip-arrow {
		--tooltipBackground: #000 !important;
		@apply !text-white !text-xs;
	}

	.tooltip-container {
		@apply !border-none !p-1;
	}

	.tooltip-arrow {
		@apply before:!border-none;
	}

	html.dark .tooltip-container,
	html.dark .tooltip-arrow {
		--tooltipBackground: #000 !important;
	}

	.scrollbar-hide::-webkit-scrollbar {
		display: none;
	}

	.scrollbar-hide {
		-ms-overflow-style: none; /* IE and Edge */
		scrollbar-width: none; /* Firefox */
	}

	.custom-scrollbar::-webkit-scrollbar {
		display: block;
		width: 8px;
		height: 5px;
		@apply dark:bg-[#7b7b7c] bg-gray-400;
	}
	.custom-scrollbar:hover::-webkit-scrollbar {
		padding: 1px;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb {
		@apply dark:bg-[#484848] bg-gray-300;
	}

	/* Details Aside Bar */

	.details-label {
		@apply not-italic font-medium text-[0.625rem] leading-[140%] tracking-[-0.02em] text-[#A5A2B2] 2xl:text-xs;
	}
}

/* input */
.search-border {
	height: 33px;
}
.search-border .input-border {
	border-width: 0px !important;
	height: 40px;
}
.search-border .input-border input {
	height: 40px;
}
.search-border .input-border input::placeholder {
	color: #7e7991;
	font-size: 15px !important;
	/* font-weight: 500; */
	letter-spacing: 0.8px;
}

#file-upload input[type='file'] {
	display: none;
}

.search-class {
	height: 100%;
	width: 100%;
	display: flex;
	align-items: center;
	background: #fcfcfc;
	border-top-left-radius: 10px;
	border-bottom-left-radius: 10px;
}

.theme-popup-scrollbar ::-webkit-scrollbar {
	display: none;
}

/* React Date picker */
.react-datepicker {
	@apply !bg-white dark:!bg-dark-high;
}

.react-datepicker__header {
	@apply !bg-white dark:!bg-dark-high;
}

.react-datepicker__current-month {
	@apply !text-default dark:!text-white;
}

.react-datepicker__day-name {
	@apply !text-default dark:!text-white;
}

.react-datepicker__day {
	@apply !text-default dark:!text-white;
}

.prose {
	max-width: unset !important;
}

.textAreaOutline:focus {
	@apply ring-0 outline-gray-200 dark:outline-none dark:border-2 dark:border-[#464242];
}

.nivo-calendar .nivo-calendar-month-legend text {
	@apply text-sm font-bold text-black;
}

@keyframes zoom-in-out {
	0% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.2);
	}
	100% {
		transform: scale(1);
	}
}

/* @keyframes pulse {
	0%,
	100% {
		opacity: 1;
	}
	50% {
		opacity: 1;
		transform: scale(1.2);
	}
} */

.progress-ring__circle {
	stroke-dasharray: 400, 400;
	transition: stroke-dashoffset 0.35s;
	transform: rotate(-90deg);
	transform-origin: 50% 50%;
}

@layer utilities {
	@layer responsive {
		.no-scrollbar::-webkit-scrollbar {
			display: block;
		}

		.no-scrollbar:hover::-webkit-scrollbar {
			display: block;
			width: 8px;
			height: 5px;
			@apply dark:bg-[#606062] bg-gray-300;
		}

		.no-scrollbar::-webkit-scrollbar-thumb {
			@apply bg-gray-400 dark:bg-dark--theme-light;
		}

		.no-scrollbar {
			-ms-overflow-style: none;
			scrollbar-width: none;
		}
		.no-scrollbar:hover {
			-ms-overflow-style: auto;
			scrollbar-width: auto;
		}
	}
}
/* PhoneInput component styles */
.phoneinput-container {
	width: 100%;
	height: 100%;
}

.phoneinput-label {
	display: block;
	margin-bottom: 0.25rem;
	font-size: 0.875rem;
	font-weight: 500;
	color: #374151;
}

.dark .phoneinput-label {
	color: #e5e7eb;
}

.phoneinput-required {
	color: #ef4444;
}

.phoneinput-wrapper {
	position: relative;
	width: 100%;
	height: 54px;
	border-radius: 10px;
	border: 1px solid #00000021;
}

.dark .phoneinput-wrapper {
	border-color: #3b3c44;
	background-color: #1b1d22;
}

.phoneinput-wrapper-error {
	border: 1px solid #ef4444;
	border-radius: 0.5rem;
}

.phoneinput-error-message {
	margin-top: 0.25rem;
	font-size: 0.75rem;
	color: #ef4444;
}

/* PhoneInput library overrides */
.PhoneInput {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
}

.PhoneInputInput {
	flex: 1;
	min-width: 0;
	height: 100%;
	border-radius: 10px;
	border: none;
	padding-left: 8px;
	outline: none;
	background-color: transparent;
	color: inherit;
	border-top-left-radius: 0;
	border-bottom-left-radius: 0;
}

.PhoneInputCountry {
	margin-right: 8px;
	margin-left: 12px;
}

.PhoneInputCountryIcon {
	width: 1.5em;
	height: 1em;
}

.PhoneInputCountrySelectArrow {
	margin-left: 0.25em;
	width: 0.3em;
}

.phoneinput-disabled {
	background-color: #fcfcfc;
	cursor: not-allowed;
}

.dark .phoneinput-disabled {
	background-color: #1b1d22;
}

/* Country dropdown styling */
.PhoneInputCountrySelect {
	position: relative;
}

html.dark .PhoneInputCountrySelect > option {
	background-color: #1b1d22;
	color: #e0e0e0;
}

html.dark .PhoneInputInput:disabled {
	color: #9ca3af;
}

html.dark .PhoneInput .PhoneInputCountrySelect__dropdown {
	background-color: #1b1d22;
	color: #e0e0e0;
	border-color: #3b3c44;
}

html.dark .PhoneInput .PhoneInputCountrySelect__option--highlight {
	background-color: #2a2d33;
}

html.dark .PhoneInput .PhoneInputCountrySelect__option:hover {
	background-color: #2a2d33;
}

.PhoneInputCountrySelect__dropdown-search {
	padding: 8px;
}

html.dark .PhoneInputCountrySelect__dropdown-search-input {
	background-color: #1b1d22;
	color: #e0e0e0;
	border-color: #3b3c44;
}

.PhoneInput--focus .PhoneInputInput {
	box-shadow: none;
	outline: none;
}

.PhoneInputCountrySelect__dropdown-search::before {
	content: none !important;
}

.PhoneInputCountrySelect__dropdown-search-emoji {
	display: none !important;
}

body .PhoneInputCountrySelectDropdown {
	background-color: #ffffff;
	border: 1px solid #e2e8f0;
	box-shadow:
		0 4px 6px -1px rgba(0, 0, 0, 0.1),
		0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

body.dark .PhoneInputCountrySelectDropdown {
	background-color: #1b1d22;
	border-color: #3b3c44;
	box-shadow:
		0 4px 6px -1px rgba(0, 0, 0, 0.3),
		0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

body .PhoneInputCountrySelectOption {
	padding: 8px 12px;
}

body .PhoneInputCountrySelectOption:hover,
body .PhoneInputCountrySelectOption:focus {
	background-color: #f3f4f6;
}

body.dark .PhoneInputCountrySelectOption:hover,
body.dark .PhoneInputCountrySelectOption:focus {
	background-color: #2a2d33;
}

/* Custom country selector with search */
.phoneinput-country-select {
	position: relative;
}

.phoneinput-country-button {
	display: flex;
	align-items: center;
	background: transparent;
	border: none;
	cursor: pointer;
	padding: 0 8px;
	height: 100%;
}

.phoneinput-country-name {
	margin-left: 2px;
	margin-right: 4px;
	font-size: 14px;
}

.phoneinput-dropdown-arrow {
	width: 0;
	height: 0;
	border-left: 4px solid transparent;
	border-right: 4px solid transparent;
	border-top: 5px solid currentColor;
	margin-left: 4px;
}

.phoneinput-dropdown {
	position: absolute;
	z-index: 999;
	top: calc(100% + 4px);
	left: 0;
	width: 300px;
	max-height: 300px;
	display: flex;
	flex-direction: column;
	background-color: #fff;
	border: 1px solid #e2e8f0;
	border-radius: 6px;
	box-shadow:
		0 4px 6px -1px rgba(0, 0, 0, 0.1),
		0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dark .phoneinput-dropdown {
	background-color: #1b1d22;
	border-color: #3b3c44;
	box-shadow:
		0 4px 6px -1px rgba(0, 0, 0, 0.3),
		0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

/* Search container */
.phoneinput-search-container {
	position: sticky;
	top: 0;
	z-index: 1;
	padding: 8px;
	background-color: #fff;
	border-bottom: 1px solid #e2e8f0;
	border-top-left-radius: 6px;
	border-top-right-radius: 6px;
}

.dark .phoneinput-search-container {
	background-color: #1b1d22;
	border-bottom-color: #3b3c44;
}

.phoneinput-search-input {
	width: 100%;
	padding: 8px 12px 8px 32px;
	border: 1px solid #e2e8f0;
	border-radius: 4px;
	font-size: 14px;
}

.dark .phoneinput-search-input {
	background-color: #1b1d22;
	border-color: #3b3c44;
	color: #e0e0e0;
}

.phoneinput-search-input:focus {
	outline: none;
	border-color: #6366f1;
}

.dark .phoneinput-search-input:focus {
	border-color: #818cf8;
}

.phoneinput-search-icon {
	position: absolute;
	left: 16px;
	top: 50%;
	transform: translateY(-50%);
	color: #9ca3af;
	pointer-events: none;
}

/* Countries container */
.phoneinput-countries-container {
	flex: 1;
	overflow-y: auto;
	max-height: 250px;
}

.phoneinput-country-list {
	list-style: none;
	margin: 0;
	padding: 0;
}

.phoneinput-country-option {
	display: flex;
	align-items: center;
	padding: 8px 12px;
	cursor: pointer;
}

.phoneinput-country-option:hover {
	background-color: #f3f4f6;
}

.dark .phoneinput-country-option:hover {
	background-color: #2a2d33;
}

.phoneinput-selected {
	background-color: #f3f4f6;
}

.dark .phoneinput-selected {
	background-color: #2a2d33;
}

.phoneinput-no-results {
	padding: 12px;
	text-align: center;
	color: #6b7280;
}

.dark .phoneinput-no-results {
	color: #9ca3af;
}

/* Flag spacing in country selector */
.phoneinput-flag-container {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	margin-right: 12px;
	min-width: 24px;
}

.phoneinput-country-label {
	flex: 1;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

/* Ensure flag icons have consistent size */
.phoneinput-country-option img,
.phoneinput-country-button img,
.phoneinput-flag-container img {
	width: 20px;
	height: auto;
	object-fit: contain;
}

/* Styles for disabled country select */
.phoneinput-country-disabled {
	pointer-events: none;
	opacity: 0.8;
}

.phoneinput-country-disabled .phoneinput-country-button {
	cursor: not-allowed;
}

.phoneinput-dropdown-arrow-disabled {
	opacity: 0.5;
}

.phoneinput-country-button:disabled {
	cursor: not-allowed;
	opacity: 0.8;
}

.phoneinput-container:has(.phoneinput-disabled) .phoneinput-country-button {
	cursor: not-allowed;
}

.phoneinput-container:has(.phoneinput-disabled) .phoneinput-country-name,
.phoneinput-container:has(.phoneinput-disabled) .phoneinput-dropdown-arrow {
	opacity: 0.7;
}

.phoneinput-container:has(.phoneinput-disabled) .phoneinput-country-button:hover {
	background-color: transparent;
}

.phoneinput-container:has(.phoneinput-disabled) .phoneinput-country-button {
	background-color: #fcfcfc;
}

.dark .phoneinput-container:has(.phoneinput-disabled) .phoneinput-country-button {
	background-color: #1b1d22;
}

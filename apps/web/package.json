{"name": "@ever-teams/web", "version": "0.1.0", "description": "Ever Teams - Open Work and Project Management Platform", "license": "AGPL-3.0", "homepage": "https://ever.team", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-teams.git"}, "bugs": {"url": "https://github.com/ever-co/ever-teams/issues"}, "private": true, "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "scripts": {"dev": "yarn node env.js && yarn next dev -p 3030", "build": "yarn next build", "start": "yarn node env.js && yarn next start -p 3030", "lint": "yarn next lint", "format": "prettier --ignore-path .gitignore \"./**/*.+(ts|js|tsx)\" --write", "knip": "knip"}, "dependencies": {"@auth/core": "^0.31.0", "@emoji-mart/data": "^1.1.2", "@emoji-mart/react": "^1.1.1", "@ever-teams/constants": "*", "@ever-teams/hooks": "*", "@ever-teams/services": "*", "@ever-teams/types": "*", "@ever-teams/ui": "*", "@ever-teams/utils": "*", "@excalidraw/excalidraw": "^0.18.0", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@google-recaptcha/react": "^2.1.1", "@hcaptcha/react-hcaptcha": "^1.12.0", "@headlessui/react": "^2.2.3", "@hello-pangea/dnd": "^18.0.1", "@heroicons/react": "^2.0.12", "@hookform/resolvers": "^3.9.1", "@jitsi/react-sdk": "^1.4.4", "@jitsu/jitsu-react": "^1.3.0", "@livekit/components-react": "^2.4.1", "@livekit/components-styles": "^1.0.12", "@livekit/krisp-noise-filter": "^0.2.5", "@nivo/calendar": "^0.89.1", "@nivo/core": "^0.89.1", "@opentelemetry/api": "^1.7.0", "@opentelemetry/auto-instrumentations-node": "^0.40.1", "@opentelemetry/exporter-trace-otlp-http": "^0.45.1", "@opentelemetry/resources": "^1.18.1", "@opentelemetry/sdk-node": "^0.45.1", "@opentelemetry/semantic-conventions": "^1.18.1", "@popperjs/core": "^2.11.6", "@radix-ui/react-accordion": "^1.2.7", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-checkbox": "^1.2.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-hover-card": "^1.1.10", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-toast": "^1.2.10", "@radix-ui/react-tooltip": "^1.1.3", "@react-pdf/renderer": "^4.3.0", "@sentry/nextjs": "^7.80.0", "@tanstack/react-query": "^5.79.0", "@tanstack/react-query-devtools": "^5.79.0", "@tanstack/react-table": "^8.20.5", "@uidotdev/usehooks": "^2.4.1", "@vercel/analytics": "^0.1.6", "autoprefixer": "^10.4.12", "axios": "^1.8.4", "class-validator": "^0.14.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^1.1.1", "cookie": "^1.0.2", "cookies-next": "^5.1.0", "country-flag-icons": "^1.5.11", "date-fns": "^4.1.0", "domhandler": "^5.0.3", "embla-carousel-react": "^8.6.0", "emoji-mart": "^5.5.2", "firebase": "8.3.3", "geist": "^1.3.1", "hotkeys-js": "^3.12.0", "i18next": "^23.6.0", "jotai": "^2.9.3", "js-cookie": "^3.0.1", "jsonwebtoken": "^9.0.2", "jwt-decode": "^3.1.2", "livekit-client": "^2.4.1", "livekit-server-sdk": "^2.6.0", "lodash": "^4.17.21", "lucide-react": "^0.473.0", "moment": "^2.29.4", "moment-timezone": "^0.5.42", "nanoid": "5.0.9", "next": "^15.3.1", "next-auth": "^5.0.0-beta.18", "next-intl": "^3.3.2", "next-themes": "^0.4.6", "ni18n": "^1.1.0", "novel": "^1.0.2", "pako": "^2.1.0", "polished": "^4.2.2", "posthog-js": "^1.154.5", "qs": "^6.11.2", "react": "19.1.0", "react-colorful": "^5.6.1", "react-country-flag": "^3.1.0", "react-day-picker": "8.10.1", "react-dom": "19.1.0", "react-google-recaptcha": "^2.1.0", "react-hook-form": "^7.53.2", "react-icons": "^5.5.0", "react-loading-skeleton": "^3.1.1", "react-paginate": "^8.2.0", "react-popper": "^2.3.0", "react-popper-tooltip": "^4.4.2", "react-resizable-panels": "^2.0.19", "react-turnstile": "^1.1.4", "recharts": "^2.15.0", "sharp": "^0.33.4", "slate": "^0.90.0", "slate-history": "^0.110.3", "slate-hyperscript": "^0.77.0", "slate-react": "^0.91.0", "slate-serializers": "^0.4.0", "sonner": "^2.0.3", "string-to-color": "^2.2.2", "tailwind-merge": "^1.14.0", "zod": "^3.23.8"}, "devDependencies": {"@ever-teams/eslint-config": "*", "@ever-teams/ts-config": "*", "@next/bundle-analyzer": "^15.3.1", "@svgr/webpack": "^8.1.0", "@tailwindcss/typography": "^0.5.9", "@types/cookie": "^0.5.1", "@types/country-flag-icons": "^1.2.2", "@types/js-cookie": "^3.0.2", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^22.15.21", "@types/pako": "^2.0.0", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "@types/react-google-recaptcha": "^2.1.5", "dotenv": "^16.4.1", "eslint": "^8.28.0", "eslint-config-next": "15.2.4", "eslint-plugin-unused-imports": "^3.0.0", "knip": "^5.58.0", "postcss": "^8.4.19", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.6", "typescript": "^5.8.3"}, "prettier": {"printWidth": 120, "singleQuote": true, "semi": true, "useTabs": true, "tabWidth": 4, "arrowParens": "always", "trailingComma": "none", "quoteProps": "as-needed", "overrides": [{"files": "*.scss", "options": {"useTabs": false, "tabWidth": 2}}, {"files": "*.yml", "options": {"useTabs": false, "tabWidth": 2}}]}, "engines": {"node": ">=16.0.0", "yarn": ">=1.13.0"}, "exclude": ["/app/[locale]/layout.tsx"], "resolutions": {"@types/react": "19.1.0", "@types/react-dom": "19.1.1"}}